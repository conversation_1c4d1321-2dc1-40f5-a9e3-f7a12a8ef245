package com.tianji.remark.service.impl;

import com.tianji.api.dto.msg.LikedTimesDTO;
import com.tianji.common.autoconfigure.mq.RabbitMqHelper;
import com.tianji.common.constants.MqConstants;
import com.tianji.common.utils.CollUtils;
import com.tianji.common.utils.StringUtils;
import com.tianji.common.utils.UserContext;
import com.tianji.remark.constants.RedisConstants;
import com.tianji.remark.domain.dto.LikeRecordFormDTO;
import com.tianji.remark.domain.po.LikedRecord;
import com.tianji.remark.mapper.LikedRecordMapper;
import com.tianji.remark.service.ILikedRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.StringRedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <p>
 * 点赞记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LikedRecordServiceImpl extends ServiceImpl<LikedRecordMapper, LikedRecord> implements ILikedRecordService {

    private final RabbitMqHelper rabbitMqHeLper;
    private final StringRedisTemplate redisTemplate;

    /**
     * 添加点赞记录
     * @param dto
     */
    @Override
    public void addLikeRecord(LikeRecordFormDTO dto) {
        // 获取当前登录用户ID
        Long userId = UserContext.getUser();
        // 判断是否已经点赞
        /*boolean flag = true;
        if (dto.getLiked()){
            flag = liked(dto);
        } else {
            flag = unliked(dto);
        }*/
        boolean flag = dto.getLiked() ? liked(dto,userId) : unliked(dto,userId);
        if (!flag) {
            return;
        }
        // 统计业务的总点赞数
        /*Integer totalLikesNum = Math.toIntExact(this.lambdaQuery()
                .eq(LikedRecord::getBizId, dto.getBizId())
                .count());*/
        String key = RedisConstants.LIKE_BIZ_KEY_PREFIX + dto.getBizId();
        Long totalLikesNum = redisTemplate.opsForSet().size(key);
        if (totalLikesNum == null){
            totalLikesNum = 0L;
        }

        // 采用缓存
        String bizTypeTotalLikeKey = RedisConstants.LIKE_COUNT_KEY_PREFIX + dto.getBizType();
        // 使用ZSet存储每种业务类型的点赞数统计
        redisTemplate.opsForZSet().add(bizTypeTotalLikeKey, dto.getBizId().toString(), totalLikesNum);

        /*// 发送到mq
        log.debug("发送点赞消息，业务id：{}，业务类型：{}，点赞数：{}", dto.getBizId(), dto.getBizType(), totalLikesNum);
        String routingKey = StringUtils.format(MqConstants.Key.LIKED_TIMES_KEY_TEMPLATE, dto.getBizType());
        *//* 手动创建消息
        LikedTimesDTO msg = new LikedTimesDTO();
        msg.setBizId(dto.getBizId());
        msg.setLikedTimes(totalLikesNum);
        // 通过构造器创建消息
        LikedTimesDTO msg = LikedTimesDTO.builder().bizId(dto.getBizId()).likedTimes(totalLikesNum).build();
        *//*
        // 通过工厂方法创建消息
        LikedTimesDTO msg = LikedTimesDTO.of(dto.getBizId(), totalLikesNum.intValue());
        log.info("发送点赞数消息：{}", msg);
        rabbitMqHeLper.send( MqConstants.Exchange.LIKE_RECORD_EXCHANGE, routingKey,msg );*/
    }

    // 取消点赞
    private boolean unliked(LikeRecordFormDTO dto, Long userId) {
        // 同样使用缓存，查询点赞记录
        String key = RedisConstants.LIKE_BIZ_KEY_PREFIX + dto.getBizId();
        // 删除点赞记录
        Long result = redisTemplate.opsForSet().remove(key, userId.toString());
        log.debug("取消点赞操作结果：{}，用户ID：{}，业务ID：{}", result, userId, dto.getBizId());
        return result != null && result > 0;
        /*LikedRecord record = this.lambdaQuery()
                .eq(LikedRecord::getUserId, userId)
                .eq(LikedRecord::getBizId, dto.getBizId())
                .one();
        if (record ==  null){
            log.debug("未找到点赞记录，用户ID：{}，业务ID：{}", userId, dto.getBizId());
            return false;
        }
        // 删除点赞记录
        boolean result = this.removeById(record.getId());
        log.debug("取消点赞操作结果：{}，记录ID：{}", result, record.getId());
        return result;*/
    }

    // 点赞
    private boolean liked(LikeRecordFormDTO dto, Long userId) {
        // 基于redis实现
        String key = RedisConstants.LIKE_BIZ_KEY_PREFIX + dto.getBizId();
        // 添加点赞记录
        Long result = redisTemplate.boundSetOps(key).add(userId.toString());
        log.debug("点赞操作结果：{}，用户ID：{}，业务ID：{}", result, userId, dto.getBizId());
        return result != null && result > 0;
        /*LikedRecord record = this.lambdaQuery()
                .eq(LikedRecord::getUserId, userId)
                .eq(LikedRecord::getBizId, dto.getBizId())
                .one();
        if (record != null){
            log.debug("用户已点赞，无需重复点赞，用户ID：{}，业务ID：{}", userId, dto.getBizId());
            return false;
        }
        record = new LikedRecord();
        record.setUserId(userId);
        record.setBizId(dto.getBizId());
        record.setBizType(dto.getBizType());
        boolean result = this.save(record);
        log.debug("点赞操作结果：{}，新记录ID：{}", result, record.getId());
        return result;*/
    }

    /**
     * 查询点赞状态
     * @param bizIds 业务id
     * @return 点赞状态
     */
    @Override
    public Set<Long> getLikesStatusByBizIds(List<Long> bizIds) {
        /*// 使用Redis缓存
        // 获取用户
        Long userId = UserContext.getUser();
        if (CollUtils.isNotEmpty(bizIds)){
            return CollUtils.emptySet();
        }
        // 循环数据
        Set<Long> retSet = new HashSet<>();
        bizIds.forEach(bizId -> {
            String key = RedisConstants.LIKE_BIZ_KEY_PREFIX +  bizId;
            Boolean member = redisTemplate.opsForSet().isMember(key, key + userId.toString());
            if (member){
                retSet.add(bizId);
            }
        });*/

        /*// 获取用户id
        Long userId = UserContext.getUser();
        // 获取点赞记录
        List<LikedRecord> recordList = this.lambdaQuery()
                .eq(LikedRecord::getUserId, userId).
                in(LikedRecord::getBizId, bizIds).list();
        // 返回查询结果
        if (recordList != null && !recordList.isEmpty()){
            return recordList.stream().map(LikedRecord::getBizId).collect(Collectors.toSet());
        }*/

        Long userId = UserContext.getUser();
        List<Object> objects = redisTemplate.executePipelined((RedisCallback<Object>) connection -> {
            StringRedisConnection src = (StringRedisConnection) connection;
            for (Long bizId : bizIds) {
                String key = RedisConstants.LIKE_BIZ_KEY_PREFIX + bizId;
                src.sIsMember(key, userId.toString());
            }
            return null;
        });

        return IntStream.range(0, bizIds.size())
                .filter(i -> (boolean)objects.get(i))
                .mapToObj(bizIds::get)
                .collect(Collectors.toSet());
    }

    /**
     * 批量获取点赞状态
     * @return 业务id对应的点赞状态
     *
     **/
    @Override
    public void readLikedTimesAndSendMessage(String bizType, int maxBizSize) {
        // 拼接key
        String key = RedisConstants.LIKE_COUNT_KEY_PREFIX + bizType;

        List<LikedTimesDTO> list = new ArrayList<>();
        Set<ZSetOperations.TypedTuple<String>> typedTuples = redisTemplate.opsForZSet().popMin(key, maxBizSize);
        for (ZSetOperations.TypedTuple<String> typedTuple : typedTuples) {
            String bizId = typedTuple.getValue();
            Double likedTimes = typedTuple.getScore();
            if (StringUtils.isBlank(bizId) || likedTimes == null){
                continue;
            }
            // 封装成DTO
            LikedTimesDTO msg = LikedTimesDTO.of(Long.valueOf(bizId), likedTimes.intValue());
            list.add(msg);
        }
        // 发送消息
        if (CollUtils.isNotEmpty(list)){
            log.debug("批量发送点赞数量 消息内容{}", list);
            String routingKey = StringUtils.format(MqConstants.Key.LIKED_TIMES_KEY_TEMPLATE, bizType);
            rabbitMqHeLper.send(MqConstants.Exchange.LIKE_RECORD_EXCHANGE, routingKey,list);
        }
    }
}
