server:
  port: 8083  #端口
  tomcat:
    uri-encoding: UTF-8   #服务编码
spring:
  profiles:
    active: dev
  application:
    name: search-service
  cloud:
    nacos:
      config:
        file-extension: yaml
        shared-configs: # 共享配置
          - data-id: shared-spring.yaml # 共享spring配置
            refresh: false
          - data-id: shared-redis.yaml # 共享redis配置
            refresh: false
          - data-id: shared-mybatis.yaml # 共享mybatis配置
            refresh: false
          - data-id: shared-logs.yaml # 共享日志配置
            refresh: false
          - data-id: shared-feign.yaml # 共享feign配置
            refresh: false
          - data-id: shared-mq.yaml # 共享MQ配置
            refresh: false
tj:
  swagger:
    enable: true
    package-path: com.tianji.search.controller
    title: 天机学堂 - 搜索系统接口文档
    description: 该服务包含课程搜索、兴趣推荐等相关功能
    contact-name: 传智教育·研究院
    contact-url: http://www.itcast.cn/
    contact-email: z<PERSON><PERSON><PERSON>@itcast.cn
    version: v1.0
    enableResponseWrap: true
  auth:
    resource:
      enable: true
      includeLoginPaths:
        - /interests/**
        - /courses/admin
  jdbc:
    database: tj_search

