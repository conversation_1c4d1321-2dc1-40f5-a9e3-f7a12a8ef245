package com.tianji.api.client.remark.fallback;

import com.tianji.api.client.remark.RemarkClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Slf4j
public class RemarkClientFallBack implements FallbackFactory<RemarkClient> {

    /**
     * 创建一个降级类
     * @param cause
     * @return
     */
    @Override
    public RemarkClient create(Throwable cause) {
        log.error("调用remark服务降级", cause);
        return new RemarkClient() {
            @Override
            public Set<Long> getLikesStatusByBizIds(List<Long> bizIds) {
                return null;
            }
        };
    }
}
