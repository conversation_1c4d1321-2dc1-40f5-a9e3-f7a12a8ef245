<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tianji.auth.mapper.PrivilegeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tianji.auth.domain.po.Privilege">
        <id column="id" property="id" />
        <result column="menu_id" property="menuId" />
        <result column="method" property="method" />
        <result column="uri" property="uri" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <select id="listRolePrivileges" resultType="com.tianji.auth.domain.po.Privilege">
        SELECT p.id, p.`menu_id`, p.`method`, p.uri, p.create_time, p.update_time
        FROM role_privilege rp
        LEFT JOIN `privilege` p ON p.id = rp.privilege_id
        WHERE rp.role_id = #{roleId}
    </select>

</mapper>
