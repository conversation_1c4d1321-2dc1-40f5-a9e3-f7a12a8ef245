import com.tianji.learning.LearningApplication;
import com.tianji.learning.domain.po.PointsBoard;
import com.tianji.learning.mapper.PointsRecordMapper;
import com.tianji.learning.service.IPointsBoardService;
import com.tianji.learning.utils.TableInfoContext;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest(classes = LearningApplication.class)
public class test {

    @Resource
    private IPointsBoardService pointsBoardService;

    @Test
    public void test() {
        TableInfoContext.setInfo("points_board_8");
        PointsBoard pointsBoard = new PointsBoard();
        pointsBoard.setId(1L);
        pointsBoard.setUserId(2L);
        pointsBoard.setPoints(100);
        pointsBoardService.save(pointsBoard);
    }
}
