package com.tianji.learning.mq;

import com.tianji.api.dto.trade.OrderBasicDTO;
import com.tianji.common.constants.MqConstants;
import com.tianji.common.utils.CollUtils;
import com.tianji.learning.service.ILearningLessonService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 课程表监听器
 */
@Component
@Slf4j
@RequiredArgsConstructor // 创建构造函数
public class LessonChangeListener {

    final ILearningLessonService lessonService;

    /**
     * // 5.发送MQ消息，通知报名成功 orderId, userId, cIds, finishTime
     *         rabbitMqHelper.send(
     *                 MqConstants.Exchange.ORDER_EXCHANGE,
     *                 MqConstants.Key.ORDER_PAY_KEY,
     *                 OrderBasicDTO.builder()
     *                         .orderId(orderId)
     *                         .userId(userId)
     *                         .courseIds(cIds)
     *                         .finishTime(order.getFinishTime())
     *                         .build()
     *         );
     * 课程被删除
     * @param dto 订单信息
     */
    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = "learning.lesson.pay.queue", durable = "true"),
            exchange = @Exchange(name = MqConstants.Exchange.ORDER_EXCHANGE, type = ExchangeTypes.TOPIC),
            key = MqConstants.Key.ORDER_PAY_KEY
    ))
    public void onMsg(OrderBasicDTO dto){
        log.debug("收到订单支付消息：用户{}，添加课程{}", dto.getUserId(), dto.getCourseIds());
        if (dto.getOrderId() == null || dto.getCourseIds() == null || CollUtils.isEmpty(dto.getCourseIds())) {
            // 不要处理异常消息，避免mq死循环
            return;
        }
        // 添加课程
        lessonService.addUserLessons(dto.getUserId(),dto.getCourseIds());
    }
}
