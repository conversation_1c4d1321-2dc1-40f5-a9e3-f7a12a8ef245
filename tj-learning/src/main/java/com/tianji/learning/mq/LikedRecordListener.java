package com.tianji.learning.mq;

import com.tianji.api.dto.msg.LikedTimesDTO;
import com.tianji.common.constants.MqConstants;
import com.tianji.learning.domain.po.InteractionReply;
import com.tianji.learning.service.IInteractionReplyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class LikedRecordListener {

    private final IInteractionReplyService replyService;

    /**
     * 监听点赞记录 消息
     */
    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(name = "qa.liked.times.queue", durable = "true"),
            exchange = @Exchange(name = MqConstants.Exchange.LIKE_RECORD_EXCHANGE, type = ExchangeTypes.TOPIC),
            key = MqConstants.Key.QA_LIKED_TIMES_KEY
    ))
    public void onMsg(List<LikedTimesDTO> dto)  {
        log.debug("MQ收到点赞消息：LikedRecordListener {}", dto);
        // 转换数据
        List<InteractionReply> replyList = new ArrayList<>();
        for (LikedTimesDTO likedTimesDTO : dto) {
            InteractionReply reply = new InteractionReply();
            reply.setId(likedTimesDTO.getBizId());
            reply.setLikedTimes(likedTimesDTO.getLikedTimes());
            replyList.add(reply);
        }
        replyService.updateBatchById(replyList);
    }

    /*public void onMsg(LikedTimesDTO dto)  {
        log.debug("MQ收到点赞消息：LikedRecordListener {}", dto);
        InteractionReply reply = replyService.getById(dto.getBizId());
        if (reply == null) {
            log.warn("点赞消息对应的数据不存在：{}", dto);
            return;
        }
        reply.setLikedTimes(dto.getLikedTimes());
        replyService.updateById(reply);
    }*/
}
