package com.tianji.learning.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tianji.api.client.course.CatalogueClient;
import com.tianji.api.client.course.CourseClient;
import com.tianji.api.dto.course.CataSimpleInfoDTO;
import com.tianji.api.dto.course.CourseFullInfoDTO;
import com.tianji.api.dto.course.CourseSimpleInfoDTO;
import com.tianji.common.domain.dto.PageDTO;
import com.tianji.common.domain.query.PageQuery;
import com.tianji.common.exceptions.BizIllegalException;
import com.tianji.common.utils.BeanUtils;
import com.tianji.common.utils.CollUtils;
import com.tianji.common.utils.DateUtils;
import com.tianji.common.utils.UserContext;
import com.tianji.learning.domain.dto.LearningPlanDTO;
import com.tianji.learning.domain.po.LearningLesson;
import com.tianji.learning.domain.po.LearningRecord;
import com.tianji.learning.domain.vo.LearningLessonVO;
import com.tianji.learning.domain.vo.LearningPlanPageVO;
import com.tianji.learning.domain.vo.LearningPlanVO;
import com.tianji.learning.enums.LessonStatus;
import com.tianji.learning.enums.PlanStatus;
import com.tianji.learning.mapper.LearningLessonMapper;
import com.tianji.learning.mapper.LearningRecordMapper;
import com.tianji.learning.service.ILearningLessonService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 学生课程表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LearningLessonServiceImpl extends ServiceImpl<LearningLessonMapper, LearningLesson> implements ILearningLessonService {

    final CourseClient courseClient;

    final CatalogueClient catalogueClient;

    final LearningRecordMapper recordMapper;

    /**
     * 批量新增用户课程表
     * @param userId 用户id
     * @param courseIds 课程id列表
     */
    @Override
    public void addUserLessons(Long userId, List<Long> courseIds) {
        List<LearningLesson> lessons = courseClient.getSimpleInfoList(courseIds).stream().map(course -> {
            LearningLesson lesson = new LearningLesson();
            lesson.setUserId(userId);
            lesson.setCourseId(course.getId());
            Integer validDuration = course.getValidDuration(); // 课程有效期
            if (validDuration != null && validDuration > 0) {
                LocalDateTime now = LocalDateTime.now();
                lesson.setCreateTime(now);
                lesson.setExpireTime(now.plusMonths(validDuration));
            }
            return lesson;
        }).collect(Collectors.toList());

        this.saveBatch(lessons);
    }

    /**
     * 分页查询用户课程表
     * @param query 分页参数
     * @return 用户课程表分页数据
     */
    @Override
    public PageDTO<LearningLessonVO> queryMyLessons(PageQuery query) {
        // 获取当前登录用户ID
        Long userId = UserContext.getUser();
        if (userId == null) {
            throw new BizIllegalException("用户未登录");
        }

        // 分页查询用户的课程表数据，按最近学习时间倒序排列
        Page<LearningLesson> page = this.lambdaQuery()
                .eq(LearningLesson::getUserId, userId)
                .page(query.toMpPage("latest_learn_time", false));
        List<LearningLesson> records = page.getRecords();
        if (CollUtils.isEmpty(records)){
            return PageDTO.empty(page);
        }

        // 查询课程基本信息
        List<Long> courseIds = records.stream().map(LearningLesson::getCourseId).collect(Collectors.toList());
        List<CourseSimpleInfoDTO> cinfos = courseClient.getSimpleInfoList(courseIds);
        if (CollUtils.isEmpty(cinfos)){
            throw new BizIllegalException("课程不存在");
        }

        // 构建课程信息映射表，方便后续关联查询
        Map<Long, CourseSimpleInfoDTO> infoDTOMap = cinfos.stream().collect(Collectors.toMap(CourseSimpleInfoDTO::getId, c -> c));

        // 组装返回结果VO列表
        List<LearningLessonVO> voList = new ArrayList<>();
        for (LearningLesson record : records){
            LearningLessonVO vo = BeanUtils.copyBean(record, LearningLessonVO.class);
            CourseSimpleInfoDTO infoDTO = infoDTOMap.get(record.getCourseId());
            if (infoDTO != null){
                vo.setCourseName(infoDTO.getName());
                vo.setCourseCoverUrl(infoDTO.getCoverUrl());
                vo.setSections(infoDTO.getSectionNum());
            }
            voList.add(vo);
        }

        return PageDTO.of(page, voList);
    }

    /**
     * 查询用户当前正在学习的课程信息
     * @return LearningLessonVO 用户当前正在学习的课程信息，如果没有正在学习的课程则返回null
     */
    @Override
    public LearningLessonVO queryMyCurrentLesson() {

        // 获取当前登录用户ID
        Long userId = UserContext.getUser();

        // 查询用户正在学习的课程，按最近学习时间倒序排列，只取第一条
        LearningLesson lesson = this.lambdaQuery()
                .eq(LearningLesson::getUserId, userId)
                .eq(LearningLesson::getStatus, LessonStatus.LEARNING)
                .orderByDesc(LearningLesson::getLatestLearnTime)
                .last("limit 1")
                .one();

        if (lesson == null){
            return null;
        }

        // 查询课程详细信息
        CourseFullInfoDTO cinfo = courseClient.getCourseInfoById(lesson.getCourseId(), false, false);
        if (cinfo == null){
            throw new BizIllegalException("课程不存在");
        }

        // 统计用户报名的课程总数
        Integer count = Math.toIntExact(this.lambdaQuery().eq(LearningLesson::getUserId, userId).count());

        // 查询最近学习的小节信息
        Long latestSectionId = lesson.getLatestSectionId();
        List<CataSimpleInfoDTO> cataSimpleInfoDTOS = catalogueClient.batchQueryCatalogue(CollUtils.singletonList(latestSectionId));
        if (CollUtils.isEmpty(cataSimpleInfoDTOS)){
            throw new BizIllegalException("小节不存在");
        }

        // 组装返回结果VO
        LearningLessonVO vo = BeanUtils.copyBean(lesson, LearningLessonVO.class);
        vo.setCourseName(cinfo.getName());
        vo.setCourseCoverUrl(cinfo.getCoverUrl());
        vo.setSections(cinfo.getSectionNum());
        vo.setCourseAmount(count);
        CataSimpleInfoDTO currentCatalogueInfo = cataSimpleInfoDTOS.get(0);
        vo.setLatestSectionName(currentCatalogueInfo.getName());
        vo.setLatestSectionIndex(currentCatalogueInfo.getCIndex());

        return vo;
    }

    /**
     * 判断课程是否在有效期
     * @param courseId 课程id
     * @return 课程有效期，0-永久有效，否则返回剩余天数
     */
    @Override
    public Long isLessonValid(Long courseId) {
        // 获取当前登录用户ID
        Long userId = UserContext.getUser();

        // 查询用户课程表数据
        LearningLesson lesson = this.lambdaQuery()
                .eq(LearningLesson::getUserId, userId)
                .eq(LearningLesson::getCourseId, courseId).one();

        if (lesson == null){
            return null;
        }

        // 判断课程是否在有效期
        LocalDateTime expireTime = lesson.getExpireTime();
        LocalDateTime now = LocalDateTime.now();
        if (expireTime.isAfter(now) && expireTime != null){
            return null;
        }
        return lesson.getId();
    }


    /**
     * 根据课程id查询课程表信息
     * @param courseId 课程id
     * @return 课程表信息
     */
    @Override
    public LearningLessonVO queryLessonByCourseId(Long courseId) {
        // 获取当前登录用户ID
        Long userId = UserContext.getUser();
        // 查询用户课程表数据
        LearningLesson lesson = this.lambdaQuery()
                .eq(LearningLesson::getUserId, userId)
                .eq(LearningLesson::getCourseId, courseId).one();

        if (lesson == null){
            return null;
        }
        // 构建返回结果VO
        LearningLessonVO vo = BeanUtils.copyBean(lesson, LearningLessonVO.class);

        return  vo;
    }


    @Override
    public void createLearningPlan(LearningPlanDTO dto) {
        // 获取当前登录用户ID
        Long userId = UserContext.getUser();

        // 查询用户课程表数据
        LearningLesson lesson = this.lambdaQuery()
                .eq(LearningLesson::getUserId, userId)
                .eq(LearningLesson::getCourseId, dto.getCourseId()).one();

        if (lesson == null){
            throw new BizIllegalException("用户没有正在学习的课程");
        }
        // 更新课程表数据
        /*lesson.setWeekFreq(dto.getFreq());
        this.updateById(lesson);*/
        // 只会更新指定的字段，效率更高
        this.lambdaUpdate()
                .set(LearningLesson::getWeekFreq,dto.getFreq())
                .set(LearningLesson::getPlanStatus,PlanStatus.PLAN_RUNNING)
                .eq(LearningLesson::getId, lesson.getId())
                .update();
    }

    @Override
    public LearningPlanPageVO queryMyPlans(PageQuery query) {
        // 获取当前用户
        Long userId = UserContext.getUser();

        // todo 积分查询待实现

        // 查询本周学习计划总数据
        QueryWrapper<LearningLesson> wrapper = new QueryWrapper<>();
        wrapper.select("sum(week_freq) as plansTotal");
        wrapper.eq("user_id", userId);
        wrapper.eq("plan_status", PlanStatus.PLAN_RUNNING);
        wrapper.in("status",LessonStatus.NOT_BEGIN,LessonStatus.LEARNING);
        Map<String, Object> map = this.getMap(wrapper);
        Integer plansTotal = 0;
        if (map != null && map.get("plansTotal") != null){
            plansTotal = Integer.valueOf(map.get("plansTotal").toString());
        }

        // 查询本周以及学习计划数据
        LocalDate now = LocalDate.now();
        LocalDateTime weekBeginTime = DateUtils.getWeekBeginTime(now);
        LocalDateTime weekEndTime = DateUtils.getWeekEndTime(now);
        Integer weekFinishedPlanNum = Math.toIntExact(recordMapper.selectCount(Wrappers.<LearningRecord>lambdaQuery()
                .eq(LearningRecord::getUserId, userId)
                .eq(LearningRecord::getFinished, true)
                .between(LearningRecord::getFinishTime, weekBeginTime, weekEndTime)));

        // 查询课表数据
        Page<LearningLesson> page = this.lambdaQuery()
                .eq(LearningLesson::getUserId, userId)
                .in(LearningLesson::getStatus, LessonStatus.NOT_BEGIN, LessonStatus.LEARNING)
                .eq(LearningLesson::getPlanStatus, PlanStatus.PLAN_RUNNING)
                .page(query.toMpPage("latest_learn_time", false));
        List<LearningLesson> records = page.getRecords();
        if (CollUtils.isEmpty(records)){
            LearningPlanPageVO vo = new LearningPlanPageVO();
            vo.setTotal(0L);
            vo.setPages(0L);
            vo.setList(CollUtils.emptyList());
            return vo;
        }

        // 调用课程服务，查询课程信息
        Set<Long> courseIds = records.stream().map(LearningLesson::getCourseId).collect(Collectors.toSet());
        List<CourseSimpleInfoDTO> cinfos = courseClient.getSimpleInfoList(courseIds);
        if (CollUtils.isEmpty(cinfos)){
            throw new BizIllegalException("课程不存在");
        }
        Map<Long, CourseSimpleInfoDTO> infoDTOMap = cinfos.stream().collect(Collectors.toMap(CourseSimpleInfoDTO::getId, c -> c));

        // 查询学习记录表，本周 当前用户下每一门课的已学习小结数量
        QueryWrapper<LearningRecord> recordQueryWrapper = new QueryWrapper<>();
        recordQueryWrapper.select("lesson_id as lessonId","count(*) as userId");
        recordQueryWrapper.eq("user_id", userId);
        recordQueryWrapper.eq("finished",true);
        recordQueryWrapper.between("finish_time", weekBeginTime,weekEndTime);
        recordQueryWrapper.groupBy("lesson_id");
        List<LearningRecord> learningRecords = recordMapper.selectList(recordQueryWrapper);

        Map<Long, Long> courseWeekFinishNum = learningRecords.stream()
                .collect(Collectors.toMap(LearningRecord::getLessonId, c -> c.getUserId()));

        // 返回结果
        LearningPlanPageVO vo = new LearningPlanPageVO();
        vo.setWeekTotalPlan(plansTotal);
        vo.setWeekFinished(weekFinishedPlanNum);
        List<LearningPlanVO> voList = new ArrayList<>();
        for (LearningLesson record : records){
            LearningPlanVO planVo = BeanUtils.copyBean(record, LearningPlanVO.class);
            CourseSimpleInfoDTO infoDTO = infoDTOMap.get(record.getCourseId());
            if (infoDTO != null){
                planVo.setCourseName(infoDTO.getName());
                planVo.setSections(infoDTO.getSectionNum());
            }
            //
            planVo.setWeekLearnedSections(courseWeekFinishNum.getOrDefault(record.getId(), 0L).intValue());
            /*Long aLong = courseWeekFinishNum.get(record.getId());
            if (aLong != null){
                planVo.setWeekLearnedSections(aLong.intValue());
            }else {
                planVo.setWeekLearnedSections(0);
            }*/

            voList.add(planVo);
        }
        /*vo.setList(voList);
        vo.setPages(page.getPages());
        vo.setTotal(page.getTotal());*/
        return vo.pageInfo(page.getTotal(),page.getTotal(), voList);
    }
}
