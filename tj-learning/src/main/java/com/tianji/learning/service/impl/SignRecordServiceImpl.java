package com.tianji.learning.service.impl;

import com.tianji.common.autoconfigure.mq.RabbitMqHelper;
import com.tianji.common.constants.MqConstants;
import com.tianji.common.exceptions.BizIllegalException;
import com.tianji.common.utils.CollUtils;
import com.tianji.common.utils.UserContext;
import com.tianji.learning.constants.RedisConstants;
import com.tianji.learning.domain.vo.SignResultVO;
import com.tianji.learning.mq.msg.SignInMessage;
import com.tianji.learning.service.ISignRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.BitFieldSubCommands;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.xml.crypto.dsig.SignatureMethod;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class SignRecordServiceImpl implements ISignRecordService {

    private final StringRedisTemplate redisTemplate;
    private final RabbitMqHelper mqHelper;

    /**
     * 签到
     * @return 签到结果
     */
    @Override
    public SignResultVO addSignRecord() {
        // 获取当前用户
        Long userId = UserContext.getUser();
        // 拼接 key
        LocalDateTime now = LocalDateTime.now();
        String format = now.format(DateTimeFormatter.ofPattern("yyyyMM"));
        String key = RedisConstants.SIGN_RECORD_KEY_PREFIX + userId.toString() + ":" + format;
        // 校验是否已经签到
        int offset = now.getDayOfMonth() - 1;
        Boolean setBit = redisTemplate.opsForValue().setBit(key, offset, true);
        if (setBit) {
            throw new BizIllegalException("今天已经签到过了");
        }
        // 计算连续的天数
        int days = countSignDays(key,now.getDayOfMonth());
        // 计算连续签到奖励积分
        int rewardPoints = 0;
        switch (days){
            case 7 : rewardPoints = 10;break;
            case 14 : rewardPoints = 20;break;
            case 28 : rewardPoints = 40;break;
        }

        // 保存积分
        mqHelper.send(MqConstants.Exchange.LIKE_RECORD_EXCHANGE, MqConstants.Key.SIGN_IN, SignInMessage.of(userId, rewardPoints + 1));

        // 返回vo
        SignResultVO vo = new SignResultVO();
        vo.setSignDays(days);
        vo.setRewardPoints(rewardPoints);
        vo.setSignPoints(1);
        return null;
    }

    /**
     * 计算连续的天数
     * @param key
     * @param dayOfMonth
     * @return
     */
    private int countSignDays(String key, int dayOfMonth) {
        // 求本月的第一天到今天所有的签到情况
        List<Long> bitField = redisTemplate.opsForValue()
                .bitField(key, BitFieldSubCommands.create()
                        .get(BitFieldSubCommands.BitFieldType
                                .unsigned(dayOfMonth)).valueAt(0));
        if (CollUtils.isNotEmpty(bitField)){
            return 0;
        }
        long num = bitField.get(0);
        log.debug("签到结果：{}",num);
        // num的二进制表示，1表示签到，0表示未签到
        int counter = 0;
        while ((num & 1) == 1){ // 循环判断当前位是否为1
            counter++;
            num = num >>> 1;// 逻辑右移一位
        }
        return counter;
    }

    /**
     * 查询签到结果
     * @return
     */
    @Override
    public Byte[] querySignRecords() {
        // 获取当前用户
        Long userId = UserContext.getUser();
        // 拼接 key
        LocalDateTime now = LocalDateTime.now();
        String format = now.format(DateTimeFormatter.ofPattern("yyyyMM"));
        String key = RedisConstants.SIGN_RECORD_KEY_PREFIX + userId.toString() + ":" + format;
        // 查询签到结果
        int dayOfMonth = now.getDayOfMonth();
        List<Long> bitField = redisTemplate.opsForValue()
                .bitField(key, BitFieldSubCommands.create()
                        .get(BitFieldSubCommands.BitFieldType.unsigned(dayOfMonth)).valueAt(0));
        if (CollUtils.isNotEmpty(bitField)){
            return new Byte[0];
        }
        Long num = bitField.get(0);
        int offset = dayOfMonth - 1;
        Byte[] arr = new Byte[dayOfMonth];
        while (offset >= 0){
            arr[offset] = (byte) (num & 1);
            num = num >>> 1;
            offset--;
        }
        return arr;
    }
}
