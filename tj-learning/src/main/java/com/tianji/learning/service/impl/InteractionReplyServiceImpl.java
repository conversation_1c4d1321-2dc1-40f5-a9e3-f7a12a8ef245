package com.tianji.learning.service.impl;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tianji.api.client.user.UserClient;
import com.tianji.api.dto.user.UserDTO;
import com.tianji.common.domain.dto.PageDTO;
import com.tianji.common.exceptions.BadRequestException;
import com.tianji.common.utils.BeanUtils;
import com.tianji.common.utils.CollUtils;
import com.tianji.common.utils.UserContext;
import com.tianji.learning.domain.dto.ReplyDTO;
import com.tianji.learning.domain.po.InteractionQuestion;
import com.tianji.learning.domain.po.InteractionReply;
import com.tianji.learning.domain.query.ReplyPageQuery;
import com.tianji.learning.domain.vo.ReplyVO;
import com.tianji.learning.enums.QuestionStatus;
import com.tianji.learning.mapper.InteractionQuestionMapper;
import com.tianji.learning.mapper.InteractionReplyMapper;
import com.tianji.learning.service.IInteractionReplyService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.tianji.common.constants.Constant.DATA_FIELD_NAME_CREATE_TIME;
import static com.tianji.common.constants.Constant.DATA_FIELD_NAME_LIKED_TIME;

/**
 * <p>
 * 互动问题的回答或评论 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InteractionReplyServiceImpl extends ServiceImpl<InteractionReplyMapper, InteractionReply> implements IInteractionReplyService {

    private final InteractionQuestionMapper questionMapper;
    private final UserClient userClient;

    /**
     * 创建回复
     * @param dto 回复信息
     */
    @Override
    public void saveReply(ReplyDTO dto) {
        // 获取当前用户
        Long user = UserContext.getUser();
        // 保存回复
        InteractionReply reply = BeanUtils.copyBean(dto, InteractionReply.class);
        reply.setUserId(user);
        this.save(reply);
        // 获取问题
        InteractionQuestion question = questionMapper.selectById(dto.getQuestionId());
        // 是否为回答
        if (dto.getAnswerId() != null) {
            // 添加回复数量
            InteractionReply byId = this.getById(dto.getAnswerId());
            byId.setReplyTimes(byId.getReplyTimes() + 1);
            this.updateById(byId);
        } else {
            // 是回答，修改最近的回答 同时累加回答下的评论次数
            question.setLatestAnswerId(reply.getId());
            question.setAnswerTimes(question.getAnswerTimes() + 1);
        }
        if (dto.getIsStudent()){
            question.setStatus(QuestionStatus.UN_CHECK);
        }
        questionMapper.updateById(question);
    }

    /**
     * 查询回复列表
     * @param query 查询参数
     */
    public PageDTO<ReplyVO> queryReplyVoPage(ReplyPageQuery query) {
        // 校验参数
        if (query.getQuestionId() == null && query.getAnswerId() == null){
            throw new BadRequestException("没有问题和回答id");
        }
        // 分页查询
        Page<InteractionReply> page = this.lambdaQuery()
                .eq(query.getQuestionId() != null, InteractionReply::getQuestionId, query.getQuestionId()) // 问题id
                .eq(InteractionReply::getAnswerId,query.getAnswerId() == null ? 0L : query.getAnswerId()) // 回答id
                .eq(InteractionReply::getHidden, false)
                .page(query.toMpPage(new OrderItem(DATA_FIELD_NAME_LIKED_TIME, false),new OrderItem(DATA_FIELD_NAME_CREATE_TIME, true)));

        List<InteractionReply> records = page.getRecords();
        if (CollUtils.isEmpty(records)){
            return PageDTO.empty(0L,0L);
        }
        // 补全其他数据
        Set<Long> uids  = new HashSet<>();
        Set<Long> targetReplyIds = new HashSet<>();
        for (InteractionReply record : records) {
            if (!record.getAnonymity()){
                uids.add(record.getUserId());
                uids.add(record.getTargetUserId());
            }
            if (record.getTargetReplyId() != null && record.getTargetReplyId() > 0){
                targetReplyIds.add(record.getTargetReplyId());
            }
        }
        // 查询目标回复，如果目标回复不是匿名的，则需要查询目标用户信息
        if (targetReplyIds.size() > 0){
            List<InteractionReply> targetReplyList = listByIds(targetReplyIds);
            Set<Long> targetUserIds = targetReplyList.stream().filter(Predicate.not(InteractionReply::getAnonymity))
                    .map(InteractionReply::getUserId)
                    .collect(Collectors.toSet());
            uids.addAll(targetUserIds);
        }

        List<UserDTO> userDTOList = userClient.queryUserByIds(uids);
        Map<Long, UserDTO> userDTOMap = new HashMap<>();
        if (CollUtils.isNotEmpty(userDTOList)){
            userDTOMap = userDTOList.stream().collect(Collectors.toMap(UserDTO::getId, u -> u));
        }
        // 封装结果
        List<ReplyVO> voList = new ArrayList<>();
        for (InteractionReply r : records){
            ReplyVO vo = BeanUtils.copyBean(r, ReplyVO.class);
            if (!r.getAnonymity()){
                UserDTO userDTO = userDTOMap.get(r.getUserId());
                if (userDTO != null){
                    vo.setUserName(userDTO.getName());
                    vo.setUserIcon(userDTO.getIcon());
                    vo.setUserType(userDTO.getType());
                }
            }
            UserDTO targetUserDTO = userDTOMap.get(r.getTargetUserId());
            if (targetUserDTO != null){
                vo.setTargetUserName(targetUserDTO.getName());
            }
            voList.add(vo);
        }
        return PageDTO.of(page, voList);
    }
}
