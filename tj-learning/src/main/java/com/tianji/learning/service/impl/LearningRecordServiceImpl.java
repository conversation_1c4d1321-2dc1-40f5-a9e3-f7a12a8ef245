package com.tianji.learning.service.impl;

import com.tianji.api.client.course.CourseClient;
import com.tianji.api.dto.course.CourseFullInfoDTO;
import com.tianji.api.dto.leanring.LearningLessonDTO;
import com.tianji.api.dto.leanring.LearningRecordDTO;
import com.tianji.common.exceptions.BizIllegalException;
import com.tianji.common.exceptions.DbException;
import com.tianji.common.utils.BeanUtils;
import com.tianji.common.utils.UserContext;
import com.tianji.learning.domain.dto.LearningRecordFormDTO;
import com.tianji.learning.domain.po.LearningLesson;
import com.tianji.learning.domain.po.LearningRecord;
import com.tianji.learning.enums.LessonStatus;
import com.tianji.learning.enums.SectionType;
import com.tianji.learning.mapper.LearningRecordMapper;
import com.tianji.learning.service.ILearningLessonService;
import com.tianji.learning.service.ILearningRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tianji.learning.utils.LearningRecordDelayTaskHandler;
import groovy.util.logging.Slf4j;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 学习记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class LearningRecordServiceImpl extends ServiceImpl<LearningRecordMapper, LearningRecord> implements ILearningRecordService {

    private final ILearningLessonService lessonService;
    private final CourseClient courseClient;
    private final LearningRecordDelayTaskHandler taskHandler;

    /**
     * 查询当前用户指定课程的学习进度
     *
     * @param courseId 课程id
     * @return 学习进度信息
     */
    @Override
    public LearningLessonDTO queryLearningRecordByCourse(Long courseId) {
        // 获取当前登录用户ID
        Long userId = UserContext.getUser();
        // 查询用户正在学习的课程
        LearningLesson lesson = lessonService.lambdaQuery()
                .eq(LearningLesson::getUserId, userId)
                .eq(LearningLesson::getCourseId, courseId)
                .one();
        if (lesson == null){
            throw new BizIllegalException("用户没有正在学习的课程");
        }
        // 查询用户正在学习的课程的记录
        List<LearningRecord> recordList = this.lambdaQuery()
                .eq(LearningRecord::getUserId, userId)
                .eq(LearningRecord::getLessonId, lesson.getId())
                .list();
        // 封装数据并返回
        LearningLessonDTO lessonDTO = new LearningLessonDTO();
        if (recordList != null && !recordList.isEmpty()){
            lessonDTO.setId(lesson.getId());
            lessonDTO.setLatestSectionId(lesson.getLatestSectionId());
            List<LearningRecordDTO> dtoList = BeanUtils.copyList(recordList, LearningRecordDTO.class);
            lessonDTO.setRecords(dtoList);
            return lessonDTO;
        }
        return lessonDTO;
    }

    /**
     * 提交学习记录
     *
     * @param dto 学习记录信息
     */
    @Override
    public void addLearningRecord(LearningRecordFormDTO dto) {
        // 获取当前 用户
        Long userId = UserContext.getUser();
        // 1.校验参数 2.处理参数
        boolean isFinished = false;
        if (dto.getSectionType().equals(SectionType.VIDEO)){
            isFinished = handlerVideoRecord(dto, userId);
        } else {
            // 提交考试
            isFinished = handlerExamRecord(dto, userId);
        }
        // 3.处理课程数据
        if (!isFinished) return;
        handleLessonData(dto);
    }

    // 处理课程数据
    private void handleLessonData(LearningRecordFormDTO dto) {
        // 1.查询课程 learning_lesson
        LearningLesson lesson = lessonService.getById(dto.getLessonId());
        if (lesson == null) {
            throw new BizIllegalException("课表不存在");
        }
        //
        boolean allFinished = false;
        CourseFullInfoDTO cinfo = courseClient.getCourseInfoById(lesson.getCourseId(), false, false);
        if (cinfo == null){
            throw new BizIllegalException("课程不存在");
        }
        Integer sectionNum = cinfo.getSectionNum();
        //
        Integer learnedSections = lesson.getLearnedSections();
        allFinished = learnedSections + 1 >= sectionNum;
        // 更新课表数据
        lessonService.lambdaUpdate()
                .set(lesson.getStatus() == LessonStatus.NOT_BEGIN,LearningLesson::getStatus, LessonStatus.LEARNING)
                .set(allFinished,LearningLesson::getStatus, LessonStatus.FINISHED)
                .set(LearningLesson::getLatestSectionId,dto.getSectionId())
                .set(LearningLesson::getLatestLearnTime,dto.getCommitTime())
                .set(LearningLesson::getLearnedSections,lesson.getLearnedSections() + 1)
                .eq(LearningLesson::getId, lesson.getId())
                .update();

    }

    // 处理视频学习记录
    private boolean handlerVideoRecord(LearningRecordFormDTO dto, Long userId) {
        // 1.查询旧的学习记录 learning_record
        LearningRecord learningRecord = queryOldRecord(dto.getLessonId(), dto.getSectionId());

                /*LearningRecord record = this.lambdaQuery().eq(LearningRecord::getUserId, userId)
                .eq(LearningRecord::getLessonId, dto.getLessonId())
                .eq(LearningRecord::getSectionId, dto.getSectionId())
                .one();*/
        // 2.判断是否存在
        if (learningRecord == null){
            // 不存在则新增
            learningRecord = BeanUtils.toBean(dto, LearningRecord.class);
            learningRecord.setUserId(userId);
            boolean save = this.save(learningRecord);
            if (!save){
                throw new DbException("保存学习记录失败");
            }
            return false;
        }
        // 3.如果存在，则更新学习记录
        boolean isFinished = !learningRecord.getFinished() && dto.getMoment() * 2 >= dto.getDuration();
        // 3.1.更新学习记录
        if (!isFinished){
            LearningRecord record = new LearningRecord();
            record.setLessonId(dto.getLessonId());
            record.setSectionId(dto.getSectionId());
            record.setMoment(dto.getMoment());
            record.setFinished(learningRecord.getFinished());
            record.setId(learningRecord.getId());
            taskHandler.addLearningRecordTask(record);
            return false;
        }

        boolean result = this.lambdaUpdate().set(LearningRecord::getMoment, dto.getMoment())
                .set(isFinished, LearningRecord::getFinished, true)
                .set(isFinished, LearningRecord::getFinishTime, dto.getCommitTime())
                .eq(LearningRecord::getId, learningRecord.getId())
                .update();
        if (!result){
            throw new DbException("更新学习记录失败");
        }
        // 清理缓存
        taskHandler.cleanRecordCache(dto.getLessonId(), dto.getSectionId());

        return isFinished;
    }

    // 利用缓存处理视频学习记录
    private LearningRecord queryOldRecord(Long lessonId, Long sectionId) {
        // 1.从缓存中获取学习记录
        LearningRecord cache = taskHandler.readRecordCache(lessonId, sectionId);
        if (cache != null)  return cache;
        // 2.从数据库中获取学习记录
        LearningRecord dbRecord = this.lambdaQuery()
                .eq(LearningRecord::getLessonId, lessonId)
                .eq(LearningRecord::getSectionId, sectionId)
                .one();
        if (dbRecord == null)  return null;
        // 3.写入缓存
        taskHandler.writeRecordCache(dbRecord);
        return dbRecord;
    }

    // 处理考试学习记录
    private boolean handlerExamRecord(LearningRecordFormDTO dto, Long userId) {
        // 转换为po
        LearningRecord record = BeanUtils.copyBean(dto, LearningRecord.class);
        record.setUserId(userId);
        record.setFinished(true);
        record.setFinishTime(dto.getCommitTime());
        // 保存学习记录
        boolean save = this.save(record);
        if (!save){
            throw new DbException("保存学习记录失败");
        }
        return true;
    }
}
