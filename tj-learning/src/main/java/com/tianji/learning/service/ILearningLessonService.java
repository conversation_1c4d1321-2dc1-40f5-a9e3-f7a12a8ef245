package com.tianji.learning.service;

import com.tianji.common.domain.dto.PageDTO;
import com.tianji.common.domain.query.PageQuery;
import com.tianji.learning.domain.dto.LearningPlanDTO;
import com.tianji.learning.domain.po.LearningLesson;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tianji.learning.domain.vo.LearningLessonVO;
import com.tianji.learning.domain.vo.LearningPlanPageVO;

import java.util.List;

/**
 * <p>
 * 学生课程表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
public interface ILearningLessonService extends IService<LearningLesson> {

    /**
     * 添加用户课程表
     * @param userId 用户id
     * @param courseIds 课程id列表
     */
    void addUserLessons(Long userId, List<Long> courseIds);

    /**
     * 分页查询用户课程表
     * @param query 分页参数
     * @return 课程表列表
     */
    PageDTO<LearningLessonVO> queryMyLessons(PageQuery query);

    LearningLessonVO queryMyCurrentLesson();

    /**
     * 查询课程表是否有效
     * @param courseId 课程id
     * @return 课程表是否有效
     */
    Long isLessonValid(Long courseId);

    /**
     * 查询用户指定的课程状态
     * @param courseId 课程id
     * @return 课程表
     */
    LearningLessonVO queryLessonByCourseId(Long courseId);

    /**
     * 创建学习计划
     * @param dto 学习计划信息
     */
    void createLearningPlan(LearningPlanDTO dto);

    /**
     * 查询用户学习计划
     * @param query 分页参数
     * @return 学习计划分页结果
     */
    LearningPlanPageVO queryMyPlans(PageQuery query);
}
