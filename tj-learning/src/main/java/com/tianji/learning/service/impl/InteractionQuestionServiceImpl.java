package com.tianji.learning.service.impl;

import com.baomidou.mybatisplus.core.metadata.TableFieldInfo;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tianji.api.cache.CategoryCache;
import com.tianji.api.client.course.CatalogueClient;
import com.tianji.api.client.course.CategoryClient;
import com.tianji.api.client.course.CourseClient;
import com.tianji.api.client.search.SearchClient;
import com.tianji.api.client.user.UserClient;
import com.tianji.api.dto.course.CataSimpleInfoDTO;
import com.tianji.api.dto.course.CourseDTO;
import com.tianji.api.dto.course.CourseSimpleInfoDTO;
import com.tianji.api.dto.user.UserDTO;
import com.tianji.common.domain.dto.PageDTO;
import com.tianji.common.exceptions.BadRequestException;
import com.tianji.common.exceptions.BizIllegalException;
import com.tianji.common.utils.BeanUtils;
import com.tianji.common.utils.CollUtils;
import com.tianji.common.utils.StringUtils;
import com.tianji.common.utils.UserContext;
import com.tianji.learning.domain.dto.QuestionFormDTO;
import com.tianji.learning.domain.po.InteractionQuestion;
import com.tianji.learning.domain.po.InteractionReply;
import com.tianji.learning.domain.query.QuestionAdminPageQuery;
import com.tianji.learning.domain.query.QuestionPageQuery;
import com.tianji.learning.domain.vo.QuestionAdminVO;
import com.tianji.learning.domain.vo.QuestionVO;
import com.tianji.learning.mapper.InteractionQuestionMapper;
import com.tianji.learning.service.IInteractionQuestionService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tianji.learning.service.IInteractionReplyService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <p>
 * 互动提问的问题表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Service
@RequiredArgsConstructor
public class InteractionQuestionServiceImpl extends ServiceImpl<InteractionQuestionMapper, InteractionQuestion> implements IInteractionQuestionService {

    private final IInteractionReplyService replyService;
    private final UserClient userClient;
    private final SearchClient searchClient;
    private final CourseClient courseClient;
    private final CatalogueClient catalogueClient;
    private final CategoryCache categoryCache;

    /**
     * 新增互动提问的问题
     * @param dto 问题表单信息
     */
    @Override
    public void saveQuestion(QuestionFormDTO dto) {
        // 获取用户 id
        Long userId = UserContext.getUser();
        InteractionQuestion question = BeanUtils.copyBean(dto, InteractionQuestion.class);
        question.setUserId(userId);
        // 保存问题
        this.save(question);
    }

    /**
     * 修改互动问题
     * @param dto 问题表单信息
     * @param id 问题id
     */
    @Override
    public void updateQuestion(QuestionFormDTO dto, Long id) {
        // 校验参数
        if (StringUtils.isBlank(dto.getTitle()) || StringUtils.isBlank(dto.getDescription()) || dto.getAnonymity() == null){
            throw new BadRequestException("参数错误");
        }
        // 获取用户 id
        InteractionQuestion question = this.getById(id);
        if (question == null){
            throw new BadRequestException("问题不存在");
        }
        // 校验用户
        if (!question.getUserId().equals(UserContext.getUser())){
            throw new BadRequestException("无权限修改");
        }
        // 修改问题
        question.setTitle(dto.getTitle());
        question.setDescription(dto.getDescription());
        question.setAnonymity(dto.getAnonymity());
        // 保存问题
        this.updateById(question);
    }

    /**
     * 分页查询互动问题
     * @param query 查询参数
     * @return 分页结果
     */
    @Override
    public PageDTO<QuestionVO> queryQuestions(QuestionPageQuery query) {
        // 校验参数
        if (query.getCourseId() == null){
            throw new BadRequestException("没有课程id");
        }
        // 获取用户 id
        Long userId = UserContext.getUser();
        // 查询问题
        Page<InteractionQuestion> page = this.lambdaQuery()
                // select的目的是为了不返回description字段，因为description字段太大了，且没有用到
                .select(InteractionQuestion.class, tableFieldInfo -> !tableFieldInfo.getProperty().equals("description"))
                .eq(InteractionQuestion::getCourseId, query.getCourseId()) // 课程id
                .eq(query.getOnlyMine(), InteractionQuestion::getUserId, userId) // 是否只查询自己的问题
                .eq(query.getSectionId() != null,InteractionQuestion::getSectionId, query.getSectionId()) // 节id
                .eq(InteractionQuestion::getHidden, false) // 是否隐藏
                .page(query.toMpPageDefaultSortByCreateTimeDesc()); // 分页查询
        // 数据转换
        List<InteractionQuestion> records = page.getRecords();
        // 获取最新回答
        if (CollUtils.isEmpty(records)){
            return PageDTO.empty(page);
        }

        Set<Long> latestAnswerIds = new HashSet<>();
        Set<Long> userIds = new HashSet<>();
        for (InteractionQuestion record : records) {
            if (!record.getAnonymity()){
                userIds.add(record.getUserId());
            }
            if (record.getLatestAnswerId() != null){
                latestAnswerIds.add(record.getLatestAnswerId());
            }
        }

        Map<Long, InteractionReply> replyMap = new HashMap<>();
        if (CollUtils.isNotEmpty(latestAnswerIds)){
//            List<InteractionReply> replyList = replyService.listByIds(latestAnswerIds);
            List<InteractionReply> replyList = replyService.list(Wrappers.<InteractionReply>lambdaQuery()
                    .in(InteractionReply::getId, latestAnswerIds)
                    .eq(InteractionReply::getHidden, false));

            for (InteractionReply reply : replyList) {
                if (!reply.getAnonymity()){
                    userIds.add(reply.getUserId());
                }
                replyMap.put(reply.getId(), reply);
            }
        }

        List<UserDTO> userDTOS = userClient.queryUserByIds(userIds);
        Map<Long, UserDTO> userDTOMap = userDTOS.stream().collect(Collectors.toMap(UserDTO::getId, u -> u));

        List<QuestionVO> voList = new ArrayList<>();
        for (InteractionQuestion r : records){
            QuestionVO vo = BeanUtils.copyBean(r, QuestionVO.class);
            if (!vo.getAnonymity()) {
                UserDTO userDTO = userDTOMap.get(r.getUserId());
                if (userDTO != null){
                    vo.setUserName(userDTO.getName());
                    vo.setUserIcon(userDTO.getIcon());
                }
            }
            InteractionReply reply = replyMap.get(r.getLatestAnswerId());
            if (reply != null){
                if (!reply.getAnonymity()) {
                    UserDTO userDTO = userDTOMap.get(reply.getUserId());
                    if (userDTO != null){
                        vo.setLatestReplyUser(userDTO.getName());
                    }
                }
                vo.setLatestReplyContent(reply.getContent());
            }
            voList.add(vo);
        }
        return PageDTO.of(page, voList);
    }

    /**
     * 根据id查询互动问题
     * @param id 问题id
     * @return 问题信息
     */
    @Override
    public QuestionVO queryQuestionById(Long id) {
        // 校验参数
        if (id == null){
            throw new BadRequestException("参数错误");
        }
        // 获取用户 id
        InteractionQuestion question = this.getById(id);
        if (question == null){
            throw new BadRequestException("问题不存在");
        }
        if (question.getHidden()){
            return null;
        }

        QuestionVO questionVO = BeanUtils.copyBean(question, QuestionVO.class);

        if (!question.getAnonymity()){
            UserDTO userDTO = userClient.queryUserById(question.getUserId());
            if (userDTO != null){
                questionVO.setUserName(userDTO.getName());
                questionVO.setUserIcon(userDTO.getIcon());
            }
        }
        return questionVO;
    }

    /**
     * 查询管理端的问题列表
     * @param query 查询参数
     * @return 问题列表
     */
    @Override
    public PageDTO<QuestionAdminVO> queryQuestionAdminVOPage(QuestionAdminPageQuery query) {

        String courseName = query.getCourseName();
        List<Long> cids = null;
        if (StringUtils.isNotEmpty(courseName)){
            cids = searchClient.queryCoursesIdByName(courseName);
            if (CollUtils.isEmpty(cids)){
                return PageDTO.empty(0L,0L);
            }
        }

        Page<InteractionQuestion> page = this.lambdaQuery()
                .in(CollUtils.isNotEmpty(cids), InteractionQuestion::getCourseId, cids)
                .eq(query.getStatus() != null, InteractionQuestion::getStatus, query.getStatus())
                .between(query.getBeginTime() != null && query.getEndTime() != null, InteractionQuestion::getCreateTime, query.getBeginTime(), query.getEndTime())
                .page(query.toMpPageDefaultSortByCreateTimeDesc());

        List<InteractionQuestion> records = page.getRecords();
        if (CollUtils.isEmpty(records)){
            return PageDTO.empty(0L,0L);
        }

        Set<Long> userIds = new HashSet<>();
        Set<Long> courseIds = new HashSet<>();
        Set<Long> chapterIds = new HashSet<>();
        for (InteractionQuestion record : records) {
            userIds.add(record.getUserId());
            courseIds.add(record.getCourseId());
            chapterIds.add(record.getChapterId());
            chapterIds.add(record.getSectionId());
        }

        List<UserDTO> userDTOS = userClient.queryUserByIds(userIds);
        if (CollUtils.isEmpty(userDTOS)){
            throw new BizIllegalException("用户数据有误");
        }
        Map<Long, UserDTO> userDTOMap = userDTOS.stream().collect(Collectors.toMap(UserDTO::getId, u -> u));

        List<CourseSimpleInfoDTO> courseDTOS = courseClient.getSimpleInfoList(courseIds);
        if (CollUtils.isEmpty(courseDTOS)){
            throw new BizIllegalException("课程数据有误");
        }
        Map<Long, CourseSimpleInfoDTO> courseDTOMap = courseDTOS.stream().collect(Collectors.toMap(CourseSimpleInfoDTO::getId, c -> c));

        List<CataSimpleInfoDTO> cataSimpleInfoDTOS = catalogueClient.batchQueryCatalogue(chapterIds);
        if (CollUtils.isEmpty(cataSimpleInfoDTOS)){
            throw new BizIllegalException("目录数据有误");
        }
        Map<Long, String> catalogueMap = cataSimpleInfoDTOS.stream().collect(Collectors.toMap(CataSimpleInfoDTO::getId, c-> c.getName()));

        // 封装结果
        List<QuestionAdminVO> voList = new ArrayList<>();
        for (InteractionQuestion record : records) {
            QuestionAdminVO adminVO = BeanUtils.copyBean(record, QuestionAdminVO.class);
            UserDTO userDTO = userDTOMap.get(record.getUserId());
            if (userDTO != null){
                adminVO.setUserName(userDTO.getName());
            }
            CourseSimpleInfoDTO courseDTO = courseDTOMap.get(record.getCourseId());
            if (courseDTO != null){
                adminVO.setCourseName(courseDTO.getName());
                String categoryNames = categoryCache.getCategoryNames(courseDTO.getCategoryIds());
                adminVO.setCategoryName(categoryNames);
            }
            adminVO.setChapterName(catalogueMap.get(record.getChapterId()));
            adminVO.setSectionName(catalogueMap.get(record.getSectionId()));
            voList.add(adminVO);
        }
        return PageDTO.of(page, voList);
    }
}
