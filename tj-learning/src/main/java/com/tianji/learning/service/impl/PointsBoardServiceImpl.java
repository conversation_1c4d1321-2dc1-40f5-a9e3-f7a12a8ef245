package com.tianji.learning.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.tianji.api.client.user.UserClient;
import com.tianji.api.dto.user.UserDTO;
import com.tianji.common.exceptions.BizIllegalException;
import com.tianji.common.utils.CollUtils;
import com.tianji.common.utils.StringUtils;
import com.tianji.common.utils.UserContext;
import com.tianji.learning.constants.RedisConstants;
import com.tianji.learning.domain.po.PointsBoard;
import com.tianji.learning.domain.query.PointsBoardQuery;
import com.tianji.learning.domain.vo.PointsBoardItemVO;
import com.tianji.learning.domain.vo.PointsBoardVO;
import com.tianji.learning.mapper.PointsBoardMapper;
import com.tianji.learning.service.IPointsBoardService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 学霸天梯榜 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@Service
@RequiredArgsConstructor
public class PointsBoardServiceImpl extends ServiceImpl<PointsBoardMapper, PointsBoard> implements IPointsBoardService {

    private final StringRedisTemplate redisTemplate;
    private final UserClient userClient;

    /**
     * 查询积分排行榜
     * @param query 查询参数
     * @return 积分排行榜
     */
    @Override
    public PointsBoardVO queryPointsBoardList(PointsBoardQuery query) {
        // 获取登录用户
        Long userId = UserContext.getUser();
        // 判断是当前赛季还是历史赛季
        boolean isCurrent = query.getSeason() == null || query.getSeason() == 0;
        // 拼接 key
        LocalDate now = LocalDate.now();
        String format = now.format(DateTimeFormatter.ofPattern("yyyyMM"));
        String key = RedisConstants.POINTS_BOARD_KEY_PREFIX  + format;
        Long season = query.getSeason();

        // 查询我的积分和排名
        PointsBoard board = isCurrent ? queryMyCurrentBoard(key) : queryMyHistoryBoard(season);

        // 分页查询赛季列表
        List<PointsBoard> list = isCurrent ? queryCurrentBoard(key,query.getPageNo(),query.getPageSize()) : queryHistoryBoard(query);

        // 封装用户id获取用户信息
        Set<Long> uids = list.stream().map(PointsBoard::getUserId).collect(Collectors.toSet());
        List<UserDTO> users = userClient.queryUserByIds(uids);
        if (CollUtils.isEmpty(users)){
            throw new BizIllegalException("用户数据有误");
        }
        // 转化为map
        Map<Long, String> userDTOMap = users.stream().collect(Collectors.toMap(UserDTO::getId, u -> u.getName()));

        // 返回vo
        PointsBoardVO vo = new PointsBoardVO();
        vo.setRank(board.getRank());
        vo.setPoints(board.getPoints());
        List<PointsBoardItemVO> boardList = new ArrayList<>();
        for (PointsBoard pointBoard : list) {
            PointsBoardItemVO itemVO = new PointsBoardItemVO();
            itemVO.setName(userDTOMap.get(pointBoard.getUserId()));
            itemVO.setRank(pointBoard.getRank());
            itemVO.setPoints(pointBoard.getPoints());
            boardList.add(itemVO);
        }
        vo.setBoardList(boardList);
        return vo;
    }

    // 查询历史赛季积分
    private List<PointsBoard> queryHistoryBoard(PointsBoardQuery query) {
        return null;
    }

    // 查询当前赛季积分
    public List<PointsBoard> queryCurrentBoard(String key, Integer pageNo,Integer pageSize) {
        // 计算起始位置
        int start = (pageNo - 1) * pageSize;
        int end = start + pageSize - 1;
        // 查询
        Set<ZSetOperations.TypedTuple<String>> typedTuples = redisTemplate.opsForZSet().reverseRangeWithScores(key, start, end);
        if (CollUtils.isEmpty(typedTuples)){
            return CollUtils.emptyList();
        }
        // 封装结果
        int rank = start + 1; // 排名从1开始
        List<PointsBoard> list = new ArrayList<>();
        for (ZSetOperations.TypedTuple<String> typedTuple : typedTuples) {
            String value = typedTuple.getValue();
            Double score = typedTuple.getScore();
            if (StringUtils.isBlank(value) || score == null){
                continue;
            }
            PointsBoard board = new PointsBoard();
            board.setPoints(score.intValue());
            board.setUserId(Long.valueOf(value));
            board.setRank(rank++);
            list.add(board);
        }
        return list;
    }

    // 查询我的历史赛季积分
    private PointsBoard queryMyHistoryBoard(Long season) {
        return null;
    }

    // 查询我的当前赛季积分
    private PointsBoard queryMyCurrentBoard(String key) {
        Long userId = UserContext.getUser();
        Double score = redisTemplate.opsForZSet().score(key, userId.toString());
        Long rank = redisTemplate.opsForZSet().reverseRank(key, userId.toString());

        PointsBoard board = new PointsBoard();
        board.setPoints(rank == null ? 0 : score.intValue());
        board.setRank(rank == null ? 0 : rank.intValue() + 1);
        return board;
    }
}
