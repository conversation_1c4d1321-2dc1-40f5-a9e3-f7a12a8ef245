package com.tianji.learning.service;

import com.tianji.api.dto.leanring.LearningLessonDTO;
import com.tianji.learning.domain.dto.LearningRecordFormDTO;
import com.tianji.learning.domain.po.LearningRecord;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 学习记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
public interface ILearningRecordService extends IService<LearningRecord> {

    /**
     * 查询当前用户指定课程的学习进度
     * @param courseId 课程id
     * @return 学习进度信息
     */
    LearningLessonDTO queryLearningRecordByCourse(Long courseId);

    /**
     * 提交学习记录
     * @param dto 学习记录信息
     */
    void addLearningRecord(LearningRecordFormDTO dto);
}
