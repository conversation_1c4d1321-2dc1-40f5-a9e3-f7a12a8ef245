package com.tianji.learning.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tianji.common.utils.CollUtils;
import com.tianji.common.utils.DateUtils;
import com.tianji.common.utils.UserContext;
import com.tianji.learning.constants.RedisConstants;
import com.tianji.learning.domain.po.PointsRecord;
import com.tianji.learning.domain.vo.PointsStatisticsVO;
import com.tianji.learning.enums.PointsRecordType;
import com.tianji.learning.mapper.PointsRecordMapper;
import com.tianji.learning.mq.msg.SignInMessage;
import com.tianji.learning.service.IPointsRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 学习积分记录，每个月底清零 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@Service
@RequiredArgsConstructor
public class PointsRecordServiceImpl extends ServiceImpl<PointsRecordMapper, PointsRecord> implements IPointsRecordService {

    final StringRedisTemplate redisTemplate;

    /**
     * 添加积分记录
     * @param msg 签到消息
     */
    @Override
    public void addPointsRecord(SignInMessage msg, PointsRecordType type) {

        if (msg.getUserId() == null || msg.getPoints() == null){
            return;
        }

        int realPoints = msg.getPoints();// 实际积分
        // 判断积分上限
        int maxPoints = type.getMaxPoints();
        if (maxPoints > 0){
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime dayStartTime = DateUtils.getDayStartTime(now);
            LocalDateTime dayEndTime = DateUtils.getDayEndTime(now);

            QueryWrapper<PointsRecord> wrapper = new QueryWrapper<>();
            wrapper.select("sum(points) as totalPoints")
                    .eq("user_id", msg.getUserId())
                    .eq("type", type)
                    .between("create_time", dayStartTime, dayEndTime);

            Map<String, Object> map = this.getMap(wrapper);
            int currentPoints = 0;
            if (map != null){
                BigDecimal totalPoints = (BigDecimal)map.get("totalPoints");
                currentPoints = totalPoints.intValue();
            }
            // 判断积分是否超出上限
            if (currentPoints > maxPoints){
                return;
            }
            // 实际的积分不能超过上限
            if (currentPoints + realPoints > maxPoints){
                realPoints = maxPoints - currentPoints;
            }
        }
        // 返回vo
        PointsRecord record = new PointsRecord();
        record.setUserId(msg.getUserId());
        record.setPoints(realPoints);
        record.setType(type);
        this.save(record);

        // 累加积分到排行榜
        LocalDate now = LocalDate.now();
        String format = now.format(DateTimeFormatter.ofPattern("yyyyMM"));
        String key = RedisConstants.POINTS_BOARD_KEY_PREFIX + format;
        redisTemplate.opsForZSet().incrementScore(key, msg.getUserId().toString(), realPoints);

    }

    /**
     * 查询今日积分记录
     * @return 积分记录
     */
    @Override
    public List<PointsStatisticsVO> queryMyTodayPoints() {
        // 获取用户id
        Long userId = UserContext.getUser();
        // 查询积分表
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime dayStartTime = DateUtils.getDayStartTime(now);
        LocalDateTime dayEndTime = DateUtils.getDayEndTime(now);

        QueryWrapper<PointsRecord> wrapper = new QueryWrapper<>();
        wrapper.select("type", "sum(points) as points")
                .eq("user_id", userId)
                .between("create_time", dayStartTime, dayEndTime)
                .groupBy("type");
        List<PointsRecord> list = this.list(wrapper);
        if (CollUtils.isEmpty(list)){
            return CollUtils.emptyList();
        }
        // 封装成vo
        List<PointsStatisticsVO> voList = new ArrayList<>();
        list.forEach(r -> {
            PointsStatisticsVO vo = new PointsStatisticsVO();
            vo.setType(r.getType().getDesc());
            vo.setMaxPoints(r.getType().getMaxPoints());
            vo.setPoints(r.getPoints());
            voList.add(vo);
        });

        return voList;
    }
}
