package com.tianji.learning.task;

import com.tianji.common.utils.CollUtils;
import com.tianji.learning.constants.RedisConstants;
import com.tianji.learning.domain.po.PointsBoard;
import com.tianji.learning.domain.po.PointsBoardSeason;
import com.tianji.learning.service.IPointsBoardSeasonService;
import com.tianji.learning.service.IPointsBoardService;
import com.tianji.learning.utils.TableInfoContext;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

import static com.tianji.learning.constants.LearningContstants.POINTS_BOARD_TABLE_PREFIX;

@Slf4j
@RequiredArgsConstructor
@Component
public class PointsBoardPersistentHandler {

    private final IPointsBoardSeasonService boardSeasonService;
    private final IPointsBoardService boardService;

//    @Scheduled(cron = "0 0 3 1 * ?")
    @XxlJob("createTableJob")
    public void createPointsBoardTableOfLastSeason(){
        log.debug("创建上赛季积分排行榜表任务");
        // 获取上个月的时间
        LocalDate time = LocalDate.now().minusMonths(1);
        // 查询赛季信息
        PointsBoardSeason one = boardSeasonService.lambdaQuery()
                .le(PointsBoardSeason::getBeginTime, time)
                .ge(PointsBoardSeason::getEndTime, time)
                .one();
        log.debug("赛季信息：{}", one);
        if (one == null){
            return;
        }
        // 创建上赛季的积分排行榜
        boardSeasonService.createPointsBoardLatestTable(one.getId());
    }

    // 持久化上月排行榜到数据库
    @XxlJob("savePointsBoard2DB")
    public void savePointsBoard2DB(){
        LocalDate time = LocalDate.now().minusMonths(1);
        // 查询赛季信息
        PointsBoardSeason one = boardSeasonService.lambdaQuery()
                .le(PointsBoardSeason::getBeginTime, time)
                .ge(PointsBoardSeason::getEndTime, time)
                .one();
        log.debug("上赛季信息：{}", one);
        if (one == null){
            return;
        }
        // 动态计算表名
        String tableName = POINTS_BOARD_TABLE_PREFIX + one.getId();
        log.debug("表名：{}", tableName);
        TableInfoContext.setInfo(tableName);
        // 分页获取数据
        String format = time.format(DateTimeFormatter.ofPattern("yyyyMM"));
        String key = RedisConstants.POINTS_BOARD_KEY_PREFIX  + format;
        int pageNo = 1;
        int pageSize = 1000;
        while (true){
            List<PointsBoard> pointsBoardList = boardService.queryCurrentBoard(key, pageNo, pageSize);
            if (CollUtils.isEmpty(pointsBoardList)){
                break;
            }
            pageNo++;
            //
            for (PointsBoard b : pointsBoardList) {
                b.setId(Long.valueOf(b.getRank()));
                b.setRank(null);
            }
            boardService.saveBatch(pointsBoardList);
        }
        // 清空threadlocal
        TableInfoContext.remove();

    }
}
