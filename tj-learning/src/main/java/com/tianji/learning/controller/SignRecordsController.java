package com.tianji.learning.controller;

import com.tianji.learning.domain.vo.SignResultVO;
import com.tianji.learning.service.ISignRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "签到记录接口")
@RestController
@RequestMapping("/sign-records")
@RequiredArgsConstructor
public class SignRecordsController {

    private final ISignRecordService recordService;

    @ApiOperation("签到")
    @PostMapping
    public SignResultVO addSignRecord() {
        return recordService.addSignRecord();
    }

    @ApiOperation("查询签到结果")
    @GetMapping
    public Byte[] querySignRecords() {
        return recordService.querySignRecords();
    }
}
