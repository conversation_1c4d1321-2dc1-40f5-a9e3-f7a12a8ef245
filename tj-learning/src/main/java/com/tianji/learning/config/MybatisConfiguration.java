package com.tianji.learning.config;

import com.baomidou.mybatisplus.extension.plugins.inner.DynamicTableNameInnerInterceptor;
import com.tianji.learning.utils.TableInfoContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
public class MybatisConfiguration {

    /**
     * 创建动态表名拦截器
     * @return
     */
    @Bean
    public DynamicTableNameInnerInterceptor dynamicTableNameInnerInterceptor() {
        DynamicTableNameInnerInterceptor interceptor = new DynamicTableNameInnerInterceptor();

        // 设置动态表名处理器
        interceptor.setTableNameHandler((sql, tableName) -> {
            // 只处理 points_board 表
            if ("points_board".equals(tableName)) {
                // 从TableInfoContext中读取保存好的动态表名
                String dynamicTableName = TableInfoContext.getInfo();
                return dynamicTableName == null ? tableName : dynamicTableName;
            }
            // 其他表名不处理
            return tableName;
        });

        return interceptor;
    }
}